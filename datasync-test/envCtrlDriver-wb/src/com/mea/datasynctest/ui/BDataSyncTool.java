package com.mea.datasynctest.ui;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BObject;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Simple test tool to verify type registration works.
 */
@NiagaraType
public class BDataSyncTool extends BObject {

    /**
     * Required Type field for all BObject subclasses.
     */
    public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

    /**
     * Get the Type of this object.
     * @return the Type
     */
    public Type getType() {
        return TYPE;
    }

    /**
     * Default constructor.
     */
    public BDataSyncTool() {
    }
}

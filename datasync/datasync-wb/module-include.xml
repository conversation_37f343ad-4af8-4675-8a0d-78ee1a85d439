<module description="Niagara 4 Data Synchronization Tool" name="datasync" preferredSymbol="nds" runtimeProfile="wb" vendor="mea" vendorVersion="1.0.0">
  <dirs>
    <dir name="com/mea/datasync/ui"/>
  </dirs>
  <types>
    <!--com.mea.datasync.ui-->
    <type class="com.mea.datasync.ui.BTestType" name="TestType"/>
    <type class="com.mea.datasync.ui.BDataSyncTool" name="DataSyncTool">
      <agent>
        <on type="workbench:Workbench"/>
      </agent>
    </type>
  </types>
  <lexicons>
    <lexicon language="en" module="datasync" resource="module.lexicon"/>
  </lexicons>
  <dependencies>
    <dependency name="baja" vendor="Tridium" vendorVersion="4.0"/>
    <dependency name="bajaui" vendor="Tridium" vendorVersion="4.0"/>
    <dependency name="workbench" vendor="Tridium" vendorVersion="4.0"/>
    <dependency name="control" vendor="Tridium" vendorVersion="4.0"/>
    <dependency name="bacnet" vendor="Tridium" vendorVersion="4.0"/>
    <dependency name="sys" vendor="Tridium" vendorVersion="4.0"/>
  </dependencies>
</module>